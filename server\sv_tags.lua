local QBCore = exports['qb-core']:GetCoreObject()

local TagsData = {
    accountIdCache = {},
    playersWithTagsActive = {},
    discordRoles = {}
}

local function GetAllPlayerGroups()
    local players = {}

    if Config.Discord.enabled then
        for playerId, roleData in pairs(TagsData.discordRoles) do
            players[tostring(playerId)] = roleData.group or 'user'
        end
    else
        local qbPlayers = QBCore.Functions.GetQBPlayers()
        for playerId, Player in pairs(qbPlayers) do
            if Player then
                players[tostring(playerId)] = Player.PlayerData.metadata.group or 'user'
            end
        end
    end

    return players
end

local function GetAllPlayerDiscordRoles()
    return TagsData.discordRoles
end

local function GetAllAccountIds()
    return TagsData.accountIdCache
end

local function LoadAccountIdForPlayer(playerId, callback)
    local Player = QBCore.Functions.GetPlayer(playerId)
    if not Player then
        if callback then callback(playerId) end
        return
    end

    if MySQL and MySQL.Async then
        MySQL.Async.fetchScalar(
            'SELECT account_id FROM users WHERE citizenid = @citizenid',
            { ['@citizenid'] = Player.PlayerData.citizenid },
            function(accountId)
                if accountId then
                    TagsData.accountIdCache[playerId] = tonumber(accountId)
                else
                    TagsData.accountIdCache[playerId] = playerId
                end

                if callback then callback(accountId or playerId) end
            end
        )
    else
        TagsData.accountIdCache[playerId] = playerId
        if callback then callback(playerId) end
    end
end

RegisterServerEvent('tags:requestPlayerGroups', function()
    local source = source
    local playerGroups = GetAllPlayerGroups()
    local playerAccountIds = GetAllAccountIds()
    local discordRoles = GetAllPlayerDiscordRoles()

    TriggerClientEvent('tags:receivePlayerGroups', source, playerGroups, playerAccountIds, discordRoles)
end)

RegisterServerEvent('tags:requestActiveTagPlayers', function()
    local source = source
    TriggerClientEvent('tags:receiveActiveTagPlayers', source, TagsData.playersWithTagsActive)
end)

RegisterServerEvent('tags:playerToggled', function(isActive)
    local source = source
    local serverId = tostring(source)

    if isActive then
        TagsData.playersWithTagsActive[serverId] = true
    else
        TagsData.playersWithTagsActive[serverId] = nil
    end

    for playerIdStr, isTagsActive in pairs(TagsData.playersWithTagsActive) do
        if isTagsActive then
            local playerId = tonumber(playerIdStr)
            if playerId then
                TriggerClientEvent('tags:receiveActiveTagPlayers', playerId, TagsData.playersWithTagsActive)
            end
        end
    end
end)

RegisterNetEvent('QBCore:Server:PlayerLoaded', function(Player)
    local playerId = Player.PlayerData.source

    LoadAccountIdForPlayer(playerId, function()
        if Config.Discord.enabled then
            exports['core_admin']:updatePlayerDiscordRole(playerId, function(roleData)
                TagsData.discordRoles[playerId] = roleData

                SetTimeout(1000, function()
                    local playerGroups = GetAllPlayerGroups()
                    local playerAccountIds = GetAllAccountIds()
                    local discordRoles = GetAllPlayerDiscordRoles()

                    for playerIdStr, isTagsActive in pairs(TagsData.playersWithTagsActive) do
                        if isTagsActive then
                            local activePlayerId = tonumber(playerIdStr)
                            if activePlayerId then
                                TriggerClientEvent('tags:receivePlayerGroups', activePlayerId, playerGroups, playerAccountIds, discordRoles)
                            end
                        end
                    end

                    TriggerClientEvent('tags:receivePlayerGroups', -1, playerGroups, playerAccountIds, discordRoles)
                end)
            end)
        else
            SetTimeout(1000, function()
                local playerGroups = GetAllPlayerGroups()
                local playerAccountIds = GetAllAccountIds()
                local discordRoles = GetAllPlayerDiscordRoles()

                for playerIdStr, isTagsActive in pairs(TagsData.playersWithTagsActive) do
                    if isTagsActive then
                        local activePlayerId = tonumber(playerIdStr)
                        if activePlayerId then
                            TriggerClientEvent('tags:receivePlayerGroups', activePlayerId, playerGroups, playerAccountIds, discordRoles)
                        end
                    end
                end

                TriggerClientEvent('tags:receivePlayerGroups', -1, playerGroups, playerAccountIds, discordRoles)
            end)
        end
    end)
end)

AddEventHandler('playerDropped', function()
    local source = source
    local serverId = tostring(source)

    TagsData.accountIdCache[source] = nil
    TagsData.playersWithTagsActive[serverId] = nil
    TagsData.discordRoles[source] = nil

    Wait(1000)

    local playerGroups = GetAllPlayerGroups()
    local playerAccountIds = GetAllAccountIds()
    local discordRoles = GetAllPlayerDiscordRoles()

    for playerIdStr, isTagsActive in pairs(TagsData.playersWithTagsActive) do
        if isTagsActive then
            local activePlayerId = tonumber(playerIdStr)
            if activePlayerId then
                TriggerClientEvent('tags:receivePlayerGroups', activePlayerId, playerGroups, playerAccountIds, discordRoles)
                TriggerClientEvent('tags:receiveActiveTagPlayers', activePlayerId, TagsData.playersWithTagsActive)
            end
        end
    end
end)


AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end

    SetTimeout(2000, function()
        local players = GetPlayers()
        local loadedCount = 0
        local totalPlayers = #players

        if totalPlayers == 0 then
            return
        end

        for _, playerId in ipairs(players) do
            local pid = tonumber(playerId)
            LoadAccountIdForPlayer(pid, function()
                if Config.Discord.enabled then
                    exports['core_admin']:updatePlayerDiscordRole(pid, function(roleData)
                        TagsData.discordRoles[pid] = roleData
                        loadedCount = loadedCount + 1

                        if loadedCount >= totalPlayers then
                            local playerGroups = GetAllPlayerGroups()
                            local playerAccountIds = GetAllAccountIds()
                            local discordRoles = GetAllPlayerDiscordRoles()

                            TriggerClientEvent('tags:receivePlayerGroups', -1, playerGroups, playerAccountIds, discordRoles)
                        end
                    end)
                else
                    loadedCount = loadedCount + 1

                    if loadedCount >= totalPlayers then
                        local playerGroups = GetAllPlayerGroups()
                        local playerAccountIds = GetAllAccountIds()
                        local discordRoles = GetAllPlayerDiscordRoles()

                        TriggerClientEvent('tags:receivePlayerGroups', -1, playerGroups, playerAccountIds, discordRoles)
                    end
                end
            end)
        end
    end)
end)

RegisterNetEvent('discord:rolesUpdated', function()
    local playerGroups = GetAllPlayerGroups()
    local playerAccountIds = GetAllAccountIds()
    local discordRoles = GetAllPlayerDiscordRoles()

    for playerIdStr, isTagsActive in pairs(TagsData.playersWithTagsActive) do
        if isTagsActive then
            local activePlayerId = tonumber(playerIdStr)
            if activePlayerId then
                TriggerClientEvent('tags:receivePlayerGroups', activePlayerId, playerGroups, playerAccountIds, discordRoles)
            end
        end
    end
end)
