local QBCore = exports['qb-core']:GetCoreObject()

local function HasPermission(source)
    if Config.Discord.enabled then
        local discordRole = exports['core_admin']:getPlayerDiscordRole(source)
        if discordRole and discordRole.group then
            for _, staffGroup in pairs(Config.StaffGroups) do
                if discordRole.group == staffGroup then
                    return true
                end
            end
        end
        return false
    else
        local Player = QBCore.Functions.GetPlayer(source)
        if not Player or not Player.PlayerData.metadata then return false end

        local group = Player.PlayerData.metadata.group
        for _, staffGroup in pairs(Config.StaffGroups) do
            if group == staffGroup then
                return true
            end
        end
        return false
    end
end

QBCore.Functions.CreateCallback('noclip:hasPermission', function(source, cb)
    cb(HasPermission(source))
end)

QBCore.Functions.CreateCallback('noclip:hasDiscordPermission', function(source, cb)
    cb(HasPermission(source))
end)

RegisterNetEvent('noclip:logUsage', function()
    local source = source
    local Player = QBCore.Functions.GetPlayer(source)

    if not Player or not <PERSON>Per<PERSON>(source) then
        return
    end

end)
