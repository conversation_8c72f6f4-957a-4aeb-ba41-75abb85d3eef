local QBCore = exports['qb-core']:GetCoreObject()

function HasNoclipPermission(source)
    return true
end

QBCore.Functions.CreateCallback('noclip:hasPermission', function(source, cb)
    cb(HasNoclipPermission(source))
end)

RegisterNetEvent('noclip:logUsage')
AddEventHandler('noclip:logUsage', function(enabled)
    local source = source
    local Player = QBCore.Functions.GetPlayer(source)

    if not Player or not HasNoclipPermission(source) then
        return
    end

    local playerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    local coords = GetEntityCoords(GetPlayerPed(source))
    local status = enabled and "ON" or "OFF"

    print(string.format('[NOCLIP] %s (%s) toggled noclip %s at %s',
        playerName, source, status, coords))
end)
