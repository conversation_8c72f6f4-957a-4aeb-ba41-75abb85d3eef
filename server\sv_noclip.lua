local QBCore = exports['qb-core']:GetCoreObject()

local function HasPermission(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player or not Player.PlayerData.metadata then return false end

    local group = Player.PlayerData.metadata.group
    for _, staffGroup in pairs(Config.StaffGroups) do
        if group == staffGroup then
            return true
        end
    end
    return false
end

QBCore.Functions.CreateCallback('noclip:hasPermission', function(source, cb)
    cb(HasPermission(source))
end)

RegisterNetEvent('noclip:logUsage', function()
    local source = source
    local Player = QBCore.Functions.GetPlayer(source)

    if not Player or not HasPer<PERSON>(source) then
        return
    end

end)
