Config = {}

Config.Framework = 'qb-core'

Config.Discord = {
    enabled = true,
    botToken = 'YOUR_BOT_TOKEN_HERE',
    guildId = 'YOUR_DISCORD_SERVER_ID_HERE',
    updateInterval = 300000,
    roleMapping = {
        ['1234567890123456789'] = { group = 'owner', stars = 5, color = 'blue' },
        ['1234567890123456788'] = { group = 'cl', stars = 4, color = 'red' },
        ['1234567890123456787'] = { group = 'admin', stars = 3, color = 'red' },
        ['1234567890123456786'] = { group = 'mod', stars = 2, color = 'yellow' },
        ['1234567890123456785'] = { group = 'helper', stars = 1, color = 'green' }
    }
}

Config.StaffGroups = {
    'owner',
    'cl',
    'admin',
    'mod',
    'helper'
}

Config.Noclip = {
    enabled = true,
    keybind = 288,
    speeds = { 0.05, 0.2, 0.8, 1.8, 3.6, 5.4, 15.0 },
    defaultSpeed = 4,
    controls = {
        moveUp = 22,
        moveDown = 36,
        moveForward = 32,
        moveBack = 33,
        rotateLeft = 34,
        rotateRight = 35,
        changeSpeed = 21,
        invisible = 44,
        mouseInput = 19
    },
    animation = {
        dict = "amb@world_human_stand_impatient@male@no_sign@base",
        name = "base"
    }
}

Config.NameTags = {
    enabled = true,
    keybind = 'F2',
    distance = 180.0,
    updateInterval = 1000,
    showHealth = true,
    showStars = true,
    showTalking = true,
    starCount = 5
}

Config.TagStars = {
    ['owner'] = 4,
    ['cl'] = 3,
    ['admin'] = 2,
    ['mod'] = 1,
    ['helper'] = 1,
    ['user'] = 0
}

Config.Colors = {
    white = 0,
    red = 6,
    blue = 48,
    green = 2,
    yellow = 5
}

Config.GroupColors = {
    ['owner'] = {
        nameActive = Config.Colors.blue,
        nameInactive = Config.Colors.white,
        star = Config.Colors.blue,
        health = Config.Colors.blue
    },
    ['cl'] = {
        nameActive = Config.Colors.red,
        nameInactive = Config.Colors.white,
        star = Config.Colors.white,
        health = Config.Colors.red
    },
    ['admin'] = {
        nameActive = Config.Colors.red,
        nameInactive = Config.Colors.white,
        star = Config.Colors.white,
        health = Config.Colors.red
    },
    ['mod'] = {
        nameActive = Config.Colors.yellow,
        nameInactive = Config.Colors.white,
        star = Config.Colors.white,
        health = Config.Colors.yellow
    },
    ['helper'] = {
        nameActive = Config.Colors.green,
        nameInactive = Config.Colors.white,
        star = Config.Colors.white,
        health = Config.Colors.green
    },
    ['user'] = {
        nameActive = Config.Colors.white,
        nameInactive = Config.Colors.white,
        star = Config.Colors.white,
        health = Config.Colors.red
    }
}

Config.Logging = {
    enabled = false,
    webhook = '',
    events = {
        noclip_toggle = true,
        staff_actions = true
    }
}
