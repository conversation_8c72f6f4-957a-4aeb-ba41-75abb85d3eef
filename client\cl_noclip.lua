local QBCore = exports['qb-core']:GetCoreObject()

local loadedAnims = false
local noclip_ANIM_A = "amb@world_human_stand_impatient@male@no_sign@base"
local noclip_ANIM_B = "base"

local in_noclip_mode = false
local travelSpeed = 4
local curLocation
local curRotation
local curHeading
local target
local mouseInput = false
featurePlayerInvisible = featurePlayerInvisible or false

exports('isNoclipping', function()
    return in_noclip_mode
end)

function HasNoclipPermission()
    return true
end

function _LoadAnimDict(dict)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Citizen.Wait(0)
    end
end

function getTableLength(T)
    local count = 0
    for _ in pairs(T) do count = count + 1 end
    return count
end

function toggleNoClipMode()
    if not HasNoclipPermission() then
        lib.notify({
            title = 'SYSTEM',
            description = 'You do not have permission to use noclip.',
            type = 'error'
        })
        return
    end

    local name = GetPlayerName(PlayerId())
    local coords = GetEntityCoords(GetPlayerPed(-1))
    local logpos = string.gsub(tostring(coords), "vector3", "")

    if(in_noclip_mode)then
        lib.notify({
            title = 'Staff',
            description = 'Noclip OFF',
            type = 'error'
        })
        turnNoClipOff()
    else
        lib.notify({
            title = 'Staff',
            description = 'Noclip ON',
            type = 'success'
        })
        turnNoClipOn()
    end
end

function turnNoClipOff()
    local playerPed = PlayerPedId()
    local inVehicle = IsPedInAnyVehicle( playerPed, false )

    if ( inVehicle ) then
        local veh = GetVehiclePedIsUsing( playerPed )
        SetEntityInvincible( veh, false )
    else
        ClearPedTasksImmediately( playerPed )
    end

    SetUserRadioControlEnabled( true )
    SetPlayerInvincible( PlayerId(), false )
    SetEntityInvincible( target, false )

    in_noclip_mode = false
end

function turnNoClipOn()
    local playerPed = PlayerPedId()
    local inVehicle = IsPedInAnyVehicle( playerPed, false )

    if ( not inVehicle ) then
        _LoadAnimDict( noclip_ANIM_A )
        loadedAnims = true
    end

    local x, y, z = table.unpack( GetEntityCoords( playerPed, false ) )
    curLocation = { x = x, y = y, z = z }
    curRotation = GetEntityRotation( playerPed, false )
    curHeading = GetEntityHeading( playerPed )

    in_noclip_mode = true
end

function degToRad( degs )
    return degs * 3.141592653589793 / 180
end

CreateThread( function()
    local rotationSpeed = 2.5
    local forwardPush = 1.8

    local speeds = { 0.05, 0.2, 0.8, 1.8, 3.6, 5.4, 15.0 }

    local moveUpKey = 22
    local moveDownKey = 36
    local moveForwardKey = 32
    local moveBackKey = 33
    local rotateLeftKey = 34
    local rotateRightKey = 35
    local changeSpeedKey = 21
    local invisKey = 44
    local mouseInputKey = 19

    function updateForwardPush()
        forwardPush = speeds[ travelSpeed ]

        local speedLabels = { "Slowest", "Slower", "Slow", "Normal", "Fast", "Faster", "Fastest" }
        lib.notify({
            title = 'NOCLIP',
            description = 'Speed: ' .. speedLabels[travelSpeed],
            type = 'inform'
        })
    end

    function handleMovement(xVect,yVect)
        if ( IsControlJustPressed( 1, changeSpeedKey ) or IsDisabledControlJustPressed( 1, changeSpeedKey ) ) then
            travelSpeed = travelSpeed + 1

            if ( travelSpeed > getTableLength(speeds) ) then
                travelSpeed = 1
            end

            updateForwardPush();
        end

        if ( IsControlJustPressed( 1, invisKey ) or IsDisabledControlJustPressed( 1, invisKey ) ) then
            featurePlayerInvisible = not featurePlayerInvisible
            featurePlayerInvisibleUpdated = true
            local invisString = featurePlayerInvisible and "ON" or "OFF"
            lib.notify({
                title = 'NOCLIP',
                description = 'Invisibility: ' .. invisString,
                type = 'inform'
            })
        end

        if ( IsControlJustPressed( 1, mouseInputKey ) or IsDisabledControlJustPressed( 1, mouseInputKey ) ) then
            mouseInput = not mouseInput
            local mouseInputString = mouseInput and "ON" or "OFF"
            lib.notify({
                title = 'NOCLIP',
                description = 'Camera Controls: ' .. mouseInputString,
                type = 'inform'
            })
        end

        if mouseInput then
            camPitch = GetGameplayCamRelativePitch()
        end

        if ( IsControlPressed( 1, moveUpKey ) or IsDisabledControlPressed( 1, moveUpKey ) ) then
            curLocation.z = curLocation.z + forwardPush / 2
        elseif ( IsControlPressed( 1, moveDownKey ) or IsDisabledControlPressed( 1, moveDownKey ) ) then
            curLocation.z = curLocation.z - forwardPush / 2
        end

        if ( IsControlPressed( 1, moveForwardKey ) or IsDisabledControlPressed( 1, moveForwardKey ) ) then
            curLocation.x = curLocation.x + xVect
            curLocation.y = curLocation.y + yVect
            if mouseInput then
                curLocation.z = curLocation.z + camPitch * forwardPush * 0.03
            end
        elseif ( IsControlPressed( 1, moveBackKey ) or IsDisabledControlPressed( 1, moveBackKey ) ) then
            curLocation.x = curLocation.x - xVect
            curLocation.y = curLocation.y - yVect
            if mouseInput then
                curLocation.z = curLocation.z - camPitch * forwardPush * 0.03
            end
        end

        if ( IsControlPressed( 1, rotateLeftKey ) or IsDisabledControlPressed( 1, rotateLeftKey ) ) then
            curHeading = curHeading + rotationSpeed
        elseif ( IsControlPressed( 1, rotateRightKey ) or IsDisabledControlPressed( 1, rotateRightKey ) ) then
            curHeading = curHeading - rotationSpeed
        end
    end

     while true do
        local playerPed = PlayerPedId()
        Citizen.Wait( 0 )

        if ( in_noclip_mode ) then

            if ( IsEntityDead( playerPed ) ) then
                turnNoClipOff()

                Wait( 100 )
            else
                target = playerPed

                local inVehicle = IsPedInAnyVehicle( playerPed, true )

                if ( inVehicle ) then
                    target = GetVehiclePedIsUsing( playerPed )
                end

                SetEntityVelocity( playerPed, 0.0, 0.0, 0.0 )
                SetEntityRotation( playerPed, 0, 0, 0, 0, false )

                SetUserRadioControlEnabled( false )
                SetPlayerInvincible( PlayerId(), true )
                SetEntityInvincible( target, true )

                if ( not inVehicle ) then
                    TaskPlayAnim( playerPed, noclip_ANIM_A, noclip_ANIM_B, 8.0, 0.0, -1, 9, 0, 0, 0, 0 )
                end

                local xVect = forwardPush * math.sin( degToRad( curHeading ) ) * -1.0
                local yVect = forwardPush * math.cos( degToRad( curHeading ) )

                handleMovement( xVect, yVect )

                SetEntityCoordsNoOffset( target, curLocation.x, curLocation.y, curLocation.z, true, true, true )
                if mouseInput then
                    local camHead = GetGameplayCamRelativeHeading()
                    local heading = GetEntityHeading(PlayerPedId())

                    local noclipOffset = camHead + heading
                    noclipOffset = noclipOffset % 360
                    SetEntityHeading( target, noclipOffset )
                    curHeading = noclipOffset
                else
                    SetEntityHeading( target, curHeading - rotationSpeed )
                end
            end
        end

        if featurePlayerInvisibleUpdated then
            SetEntityVisible(playerPed, not featurePlayerInvisible, false)
            featurePlayerInvisibleUpdated = false
        end
     end
end )

CreateThread(function()
    while true do
        Wait(0)

        if IsControlJustPressed(0, 288) or IsDisabledControlJustPressed(0, 288) then
            toggleNoClipMode()
        end
    end
end)

RegisterCommand('noclip', function()
    toggleNoClipMode()
end, false)

RegisterNUICallback('noclip', function(data, cb)
    if HasNoclipPermission() then
        toggleNoClipMode()
    end
    cb('ok')
end)
