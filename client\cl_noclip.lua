local QBCore = exports['qb-core']:GetCoreObject()

local Noclip = {
    active = false,
    speed = Config.Noclip.defaultSpeed,
    location = nil,
    rotation = nil,
    heading = nil,
    target = nil,
    mouseInput = false,
    invisible = false,
    animLoaded = false
}

local function LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(0)
        end
    end
end

local function HasPermission()
    local PlayerData = QBCore.Functions.GetPlayerData()
    if not PlayerData or not PlayerData.metadata then return false end

    local group = PlayerData.metadata.group
    for _, staffGroup in pairs(Config.StaffGroups) do
        if group == staffGroup then
            return true
        end
    end
    return false
end

local function GetTableLength(t)
    local count = 0
    for _ in pairs(t) do
        count = count + 1
    end
    return count
end

local function ToggleNoclip()
    if not HasPermission() then
        lib.notify({
            title = 'Access Denied',
            description = 'Insufficient permissions',
            type = 'error'
        })
        return
    end

    if Noclip.active then
        DisableNoclip()
        lib.notify({
            title = 'Noclip',
            description = 'Disabled',
            type = 'error'
        })
    else
        EnableNoclip()
        lib.notify({
            title = 'Noclip',
            description = 'Enabled',
            type = 'success'
        })
    end

    TriggerServerEvent('noclip:logUsage', Noclip.active)
end

local function DisableNoclip()
    local playerPed = PlayerPedId()
    local inVehicle = IsPedInAnyVehicle(playerPed, false)

    if inVehicle then
        local vehicle = GetVehiclePedIsUsing(playerPed)
        SetEntityInvincible(vehicle, false)
    else
        ClearPedTasksImmediately(playerPed)
    end

    SetUserRadioControlEnabled(true)
    SetPlayerInvincible(PlayerId(), false)
    SetEntityInvincible(Noclip.target, false)
    SetEntityVisible(playerPed, true, false)

    Noclip.active = false
    Noclip.invisible = false
end

local function EnableNoclip()
    local playerPed = PlayerPedId()
    local inVehicle = IsPedInAnyVehicle(playerPed, false)

    if not inVehicle then
        LoadAnimDict(Config.Noclip.animation.dict)
        Noclip.animLoaded = true
    end

    local coords = GetEntityCoords(playerPed, false)
    Noclip.location = { x = coords.x, y = coords.y, z = coords.z }
    Noclip.rotation = GetEntityRotation(playerPed, false)
    Noclip.heading = GetEntityHeading(playerPed)

    Noclip.active = true
end

local function DegToRad(degrees)
    return degrees * math.pi / 180
end

local function UpdateSpeed()
    local speedLabels = { "Slowest", "Slower", "Slow", "Normal", "Fast", "Faster", "Fastest" }
    lib.notify({
        title = 'Noclip Speed',
        description = speedLabels[Noclip.speed] or 'Unknown',
        type = 'inform'
    })
end

local function HandleMovement(xVector, yVector, forwardPush)
    local controls = Config.Noclip.controls

    if IsControlJustPressed(1, controls.changeSpeed) or IsDisabledControlJustPressed(1, controls.changeSpeed) then
        Noclip.speed = Noclip.speed + 1
        if Noclip.speed > GetTableLength(Config.Noclip.speeds) then
            Noclip.speed = 1
        end
        UpdateSpeed()
    end

    if IsControlJustPressed(1, controls.invisible) or IsDisabledControlJustPressed(1, controls.invisible) then
        Noclip.invisible = not Noclip.invisible
        lib.notify({
            title = 'Invisibility',
            description = Noclip.invisible and 'Enabled' or 'Disabled',
            type = 'inform'
        })
    end

    if IsControlJustPressed(1, controls.mouseInput) or IsDisabledControlJustPressed(1, controls.mouseInput) then
        Noclip.mouseInput = not Noclip.mouseInput
        lib.notify({
            title = 'Camera Controls',
            description = Noclip.mouseInput and 'Enabled' or 'Disabled',
            type = 'inform'
        })
    end

    local camPitch = Noclip.mouseInput and GetGameplayCamRelativePitch() or 0

    if IsControlPressed(1, controls.moveUp) or IsDisabledControlPressed(1, controls.moveUp) then
        Noclip.location.z = Noclip.location.z + forwardPush / 2
    elseif IsControlPressed(1, controls.moveDown) or IsDisabledControlPressed(1, controls.moveDown) then
        Noclip.location.z = Noclip.location.z - forwardPush / 2
    end

    if IsControlPressed(1, controls.moveForward) or IsDisabledControlPressed(1, controls.moveForward) then
        Noclip.location.x = Noclip.location.x + xVector
        Noclip.location.y = Noclip.location.y + yVector
        if Noclip.mouseInput then
            Noclip.location.z = Noclip.location.z + camPitch * forwardPush * 0.03
        end
    elseif IsControlPressed(1, controls.moveBack) or IsDisabledControlPressed(1, controls.moveBack) then
        Noclip.location.x = Noclip.location.x - xVector
        Noclip.location.y = Noclip.location.y - yVector
        if Noclip.mouseInput then
            Noclip.location.z = Noclip.location.z - camPitch * forwardPush * 0.03
        end
    end

    if IsControlPressed(1, controls.rotateLeft) or IsDisabledControlPressed(1, controls.rotateLeft) then
        Noclip.heading = Noclip.heading + 2.5
    elseif IsControlPressed(1, controls.rotateRight) or IsDisabledControlPressed(1, controls.rotateRight) then
        Noclip.heading = Noclip.heading - 2.5
    end
end

CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        Wait(0)

        if Noclip.active then
            if IsEntityDead(playerPed) then
                DisableNoclip()
                Wait(100)
            else
                Noclip.target = playerPed
                local inVehicle = IsPedInAnyVehicle(playerPed, true)

                if inVehicle then
                    Noclip.target = GetVehiclePedIsUsing(playerPed)
                end

                SetEntityVelocity(playerPed, 0.0, 0.0, 0.0)
                SetEntityRotation(playerPed, 0, 0, 0, 0, false)
                SetUserRadioControlEnabled(false)
                SetPlayerInvincible(PlayerId(), true)
                SetEntityInvincible(Noclip.target, true)

                if not inVehicle then
                    TaskPlayAnim(playerPed, Config.Noclip.animation.dict, Config.Noclip.animation.name, 8.0, 0.0, -1, 9, 0, 0, 0, 0)
                end

                local forwardPush = Config.Noclip.speeds[Noclip.speed]
                local xVector = forwardPush * math.sin(DegToRad(Noclip.heading)) * -1.0
                local yVector = forwardPush * math.cos(DegToRad(Noclip.heading))

                HandleMovement(xVector, yVector, forwardPush)

                SetEntityCoordsNoOffset(Noclip.target, Noclip.location.x, Noclip.location.y, Noclip.location.z, true, true, true)

                if Noclip.mouseInput then
                    local camHead = GetGameplayCamRelativeHeading()
                    local heading = GetEntityHeading(PlayerPedId())
                    local noclipOffset = (camHead + heading) % 360
                    SetEntityHeading(Noclip.target, noclipOffset)
                    Noclip.heading = noclipOffset
                else
                    SetEntityHeading(Noclip.target, Noclip.heading - 2.5)
                end
            end
        end

        SetEntityVisible(playerPed, not Noclip.invisible, false)
    end
end)

CreateThread(function()
    while true do
        Wait(0)
        if IsControlJustPressed(0, Config.Noclip.keybind) or IsDisabledControlJustPressed(0, Config.Noclip.keybind) then
            ToggleNoclip()
        end
    end
end)

RegisterCommand('noclip', function()
    ToggleNoclip()
end, false)

RegisterNUICallback('noclip', function(data, cb)
    if HasPermission() then
        ToggleNoclip()
    end
    cb('ok')
end)

exports('isNoclipping', function()
    return Noclip.active
end)
