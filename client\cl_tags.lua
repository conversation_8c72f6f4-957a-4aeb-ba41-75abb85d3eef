local QBCore = exports['qb-core']:GetCoreObject()

local NameTags = {
    active = false,
    cache = {},
    playerGroups = {},
    playerAccountIds = {},
    playersWithTagsActive = {}
}

local PlayerData = {}

CreateThread(function()
    while QBCore.Functions.GetPlayerData() == nil do
        Wait(100)
    end
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

RegisterNetEvent('tags:receivePlayerGroups', function(groups, accountIds)
    NameTags.playerGroups = groups or {}
    NameTags.playerAccountIds = accountIds or {}
end)

RegisterNetEvent('tags:receiveActiveTagPlayers', function(activePlayers)
    NameTags.playersWithTagsActive = activePlayers or {}

    if NameTags.active then
        for playerId in pairs(NameTags.cache) do
            UpdatePlayerNameTag(playerId)
        end
    end
end)

local function IsPlayerAdmin(group)
    for _, staffGroup in pairs(Config.StaffGroups) do
        if group == staffGroup then
            return true
        end
    end
    return false
end

local function GetPlayerGroup(serverId)
    return NameTags.playerGroups[tostring(serverId)] or 'user'
end

local function GetPlayerAccountId(serverId)
    return NameTags.playerAccountIds[tonumber(serverId)] or serverId
end

local function GetStarCount(group)
    return Config.TagStars[group] or 0
end

local function GetGroupColors(group)
    return Config.GroupColors[group] or Config.GroupColors['user']
end

local function DoesPlayerHaveTagsActive(serverId)
    return NameTags.playersWithTagsActive[tostring(serverId)] == true
end

RegisterKeyMapping('names', 'Toggle Name Tags', 'keyboard', Config.NameTags.keybind)

RegisterCommand('names', function()
    ToggleNameTags()
end, false)


local function ToggleNameTags()
    if not PlayerData then
        lib.notify({
            title = 'Name Tags',
            description = 'Player data not loaded',
            type = 'error'
        })
        return
    end

    if not IsPlayerAdmin(PlayerData.metadata.group) then
        lib.notify({
            title = 'Access Denied',
            description = 'Insufficient permissions',
            type = 'error'
        })
        return
    end

    if NameTags.active then
        NameTags.active = false
        lib.notify({
            title = 'Name Tags',
            description = 'Disabled',
            type = 'error'
        })
        TriggerServerEvent('tags:playerToggled', false)
        ClearAllNameTags()
    else
        NameTags.active = true
        lib.notify({
            title = 'Name Tags',
            description = 'Enabled',
            type = 'success'
        })
        TriggerServerEvent('tags:playerToggled', true)
        StartNameTags()
    end
end

local function ClearAllNameTags()
    for playerId, cache in pairs(NameTags.cache) do
        if cache.gamertag then
            RemoveMpGamerTag(cache.gamertag)
        end
    end
    NameTags.cache = {}
end

function UpdatePlayerNameTag(playerId)
    local playerPed = GetPlayerPed(playerId)
    local serverId = GetPlayerServerId(playerId)
    local playerName = GetPlayerName(playerId)

    if not serverId or not playerName or not DoesEntityExist(playerPed) then
        return false
    end

    local playerGroup = GetPlayerGroup(serverId)
    local starCount = Config.NameTags.starCount
    local playerHasTagsActive = DoesPlayerHaveTagsActive(serverId)
    local isLocalPlayer = playerId == PlayerId()
    local groupColors = GetGroupColors(playerGroup)

    local nameColor = Config.Colors.white
    if isLocalPlayer and NameTags.active then
        nameColor = Config.Colors.red
    elseif playerHasTagsActive then
        nameColor = groupColors.nameActive
    else
        nameColor = groupColors.nameInactive
    end

    local accountId = GetPlayerAccountId(serverId)
    local displayText = accountId .. ' - ' .. playerName

    if NameTags.cache[playerId] and NameTags.cache[playerId].gamertag then
        RemoveMpGamerTag(NameTags.cache[playerId].gamertag)
    end

    local gamertag = CreateMpGamerTag(playerPed, displayText, false, false, '', false)

    if gamertag then
        SetMpGamerTagVisibility(gamertag, 0, true)
        SetMpGamerTagColour(gamertag, 0, nameColor)

        if Config.NameTags.showHealth then
            SetMpGamerTagVisibility(gamertag, 2, true)
            SetMpGamerTagAlpha(gamertag, 2, 255)
            SetMpGamerTagHealthBarColour(gamertag, groupColors.health)
        end

        if Config.NameTags.showStars then
            local stars = GetStarCount(playerGroup)
            SetMpGamerTagWantedLevel(gamertag, stars)
            if stars > 0 then
                SetMpGamerTagVisibility(gamertag, 7, true)
                SetMpGamerTagColour(gamertag, 7, groupColors.star)
            else
                SetMpGamerTagVisibility(gamertag, 7, false)
            end
        end

        if Config.NameTags.showTalking then
            SetMpGamerTagVisibility(gamertag, 9, NetworkIsPlayerTalking(playerId))
        end

        NameTags.cache[playerId] = {
            gamertag = gamertag,
            lastUpdate = GetGameTimer()
        }

        return true
    end

    return false
end

local function StartNameTags()
    TriggerServerEvent('tags:requestPlayerGroups')
    TriggerServerEvent('tags:requestActiveTagPlayers')

    CreateThread(function()
        Wait(500)

        while NameTags.active do
            local activePlayers = GetActivePlayers()
            local currentPlayers = {}

            for _, playerId in ipairs(activePlayers) do
                currentPlayers[playerId] = true

                if not NameTags.cache[playerId] then
                    UpdatePlayerNameTag(playerId)
                end
            end

            for playerId, cache in pairs(NameTags.cache) do
                if not currentPlayers[playerId] then
                    if cache.gamertag then
                        RemoveMpGamerTag(cache.gamertag)
                    end
                    NameTags.cache[playerId] = nil
                end
            end

            if Config.NameTags.showTalking then
                for playerId, cache in pairs(NameTags.cache) do
                    if cache.gamertag then
                        SetMpGamerTagVisibility(cache.gamertag, 9, NetworkIsPlayerTalking(playerId))
                    end
                end
            end

            Wait(Config.NameTags.updateInterval)
        end

        ClearAllNameTags()
    end)
end

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        ClearAllNameTags()
    end
end)
