local DiscordAPI = {
    cache = {},
    lastUpdate = 0
}

local function GetPlayerDiscordId(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, identifier in pairs(identifiers) do
        if string.match(identifier, 'discord:') then
            return string.gsub(identifier, 'discord:', '')
        end
    end
    return nil
end

local function MakeDiscordRequest(endpoint, callback)
    if not Config.Discord.enabled or not Config.Discord.botToken then
        if callback then callback(false, nil) end
        return
    end

    PerformHttpRequest('https://discord.com/api/v10' .. endpoint, function(statusCode, response, headers)
        if statusCode == 200 then
            local data = json.decode(response)
            if callback then callback(true, data) end
        else
            if callback then callback(false, nil) end
        end
    end, 'GET', '', {
        ['Authorization'] = 'Bot ' .. Config.Discord.botToken,
        ['Content-Type'] = 'application/json'
    })
end

local function GetUserRoles(discordId, callback)
    if not discordId or not Config.Discord.guildId then
        if callback then callback({}) end
        return
    end

    local endpoint = '/guilds/' .. Config.Discord.guildId .. '/members/' .. discordId

    MakeDiscordRequest(endpoint, function(success, data)
        if success and data and data.roles then
            if callback then callback(data.roles) end
        else
            if callback then callback({}) end
        end
    end)
end

local function GetHighestRole(roles)
    local highestRole = { group = 'user', stars = 0, color = 'white' }

    for _, roleId in pairs(roles) do
        for groupName, roleData in pairs(Config.Discord.roles) do
            if roleData.roleId == roleId and roleData.stars > highestRole.stars then
                highestRole = {
                    group = groupName,
                    stars = roleData.stars,
                    color = roleData.color
                }
            end
        end
    end

    return highestRole
end

local function UpdatePlayerDiscordRole(source, callback)
    local discordId = GetPlayerDiscordId(source)
    if not discordId then
        DiscordAPI.cache[source] = { group = 'user', stars = 0, color = 'white' }
        if callback then callback(DiscordAPI.cache[source]) end
        return
    end

    GetUserRoles(discordId, function(roles)
        local roleData = GetHighestRole(roles)
        DiscordAPI.cache[source] = roleData
        if callback then callback(roleData) end
    end)
end

local function GetPlayerDiscordRole(source)
    return DiscordAPI.cache[source] or { group = 'user', stars = 0, color = 'white' }
end

local function UpdateAllPlayersRoles()
    local players = GetPlayers()
    local updateCount = 0
    local totalPlayers = #players

    if totalPlayers == 0 then return end

    for _, playerId in ipairs(players) do
        local source = tonumber(playerId)
        if source then
            UpdatePlayerDiscordRole(source, function()
                updateCount = updateCount + 1
                if updateCount >= totalPlayers then
                    TriggerClientEvent('discord:rolesUpdated', -1)
                end
            end)
        end
    end
end

RegisterNetEvent('discord:requestRole', function()
    local source = source
    UpdatePlayerDiscordRole(source, function(roleData)
        TriggerClientEvent('discord:receiveRole', source, roleData)
    end)
end)

RegisterNetEvent('discord:requestAllRoles', function()
    local source = source
    local allRoles = {}

    for playerId, roleData in pairs(DiscordAPI.cache) do
        allRoles[tostring(playerId)] = roleData
    end

    TriggerClientEvent('discord:receiveAllRoles', source, allRoles)
end)

AddEventHandler('playerConnecting', function()
    local source = source
    SetTimeout(5000, function()
        UpdatePlayerDiscordRole(source)
    end)
end)

AddEventHandler('playerDropped', function()
    local source = source
    DiscordAPI.cache[source] = nil
end)

CreateThread(function()
    while true do
        if Config.Discord.enabled then
            UpdateAllPlayersRoles()
        end
        Wait(Config.Discord.updateInterval)
    end
end)

AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    if Config.Discord.enabled then
        SetTimeout(3000, function()
            UpdateAllPlayersRoles()
        end)
    end
end)

exports('getPlayerDiscordRole', GetPlayerDiscordRole)
exports('updatePlayerDiscordRole', UpdatePlayerDiscordRole)
