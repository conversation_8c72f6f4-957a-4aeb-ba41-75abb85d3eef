# Discord Role Integration Setup Guide

This guide will help you set up Discord role-based permissions for your FiveM staff system.

## Prerequisites

1. A Discord server where you want to manage staff roles
2. A Discord bot with appropriate permissions
3. FiveM server with this resource installed

## Step 1: Create a Discord Bot

1. Go to https://discord.com/developers/applications
2. Click "New Application" and give it a name
3. Go to the "Bot" section
4. Click "Add Bot"
5. Copy the bot token (you'll need this for the config)
6. Enable the following bot permissions:
   - Read Messages/View Channels
   - Read Message History

## Step 2: Invite Bot to Your Server

1. Go to the "OAuth2" > "URL Generator" section
2. Select "bot" scope
3. Select the permissions mentioned above
4. Copy the generated URL and open it in your browser
5. Select your Discord server and authorize the bot

## Step 3: Get Your Discord Server ID

1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
2. Right-click on your Discord server name
3. Click "Copy ID"
4. This is your Guild ID

## Step 4: Get Role IDs

1. In your Discord server, right-click on each staff role
2. Click "Copy ID"
3. Note down each role ID with its corresponding staff level

## Step 5: Configure the Resource

Edit `config/config.lua` and update the Discord section:

```lua
Config.Discord = {
    enabled = true,
    botToken = 'YOUR_BOT_TOKEN_HERE',
    guildId = 'YOUR_DISCORD_SERVER_ID_HERE',
    updateInterval = 300000, -- 5 minutes in milliseconds
    roleMapping = {
        ['OWNER_ROLE_ID'] = { group = 'owner', stars = 5, color = 'blue' },
        ['CL_ROLE_ID'] = { group = 'cl', stars = 4, color = 'red' },
        ['ADMIN_ROLE_ID'] = { group = 'admin', stars = 3, color = 'red' },
        ['MOD_ROLE_ID'] = { group = 'mod', stars = 2, color = 'yellow' },
        ['HELPER_ROLE_ID'] = { group = 'helper', stars = 1, color = 'green' }
    }
}
```

Replace:
- `YOUR_BOT_TOKEN_HERE` with your bot token from Step 1
- `YOUR_DISCORD_SERVER_ID_HERE` with your server ID from Step 3
- Each `ROLE_ID` with the actual Discord role IDs from Step 4

## Step 6: Available Colors

You can use these colors for the star system:
- `white` - White stars
- `red` - Red stars
- `blue` - Blue stars
- `green` - Green stars
- `yellow` - Yellow stars

## Step 7: Testing

1. Restart your FiveM resource
2. Join your server with a Discord account that has one of the configured roles
3. Use the nametag toggle command to see if your stars and colors appear correctly
4. Test noclip permissions based on your Discord role

## How It Works

- The system checks Discord roles every 5 minutes (configurable)
- Players get the highest role they have in Discord
- Star count and color are determined by Discord role
- Noclip permissions are also based on Discord roles
- If Discord integration is disabled, it falls back to QBCore groups

## Troubleshooting

1. **Bot not responding**: Check bot token and permissions
2. **No roles detected**: Verify Guild ID and role IDs
3. **Players not getting permissions**: Ensure they have the Discord role and have reconnected
4. **Stars not showing**: Check color configuration and role mapping

## Security Notes

- Keep your bot token secure and never share it
- The bot only needs read permissions, never give it admin rights
- Role updates happen automatically but may take up to 5 minutes
- Players need to reconnect after role changes for immediate effect
